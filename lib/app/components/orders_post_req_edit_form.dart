import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/rounded_shadow_container.dart';
import 'package:muyipork/app/data/models/other/city.dart';
import 'package:muyipork/app/data/models/other/district.dart';
import 'package:muyipork/app/data/models/req/orders_post_req.dart';
import 'package:muyipork/app/data/models/res/orders_get_res.dart';
import 'package:muyipork/app/data/models/res/setting_get_res.dart';
import 'package:muyipork/app/modules/tables_selection/controllers/tables_selection_controller.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/table_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:tuple/tuple.dart';

class OrdersPostReqEditForm extends StatefulWidget {
  OrdersPostReqEditForm({
    @required this.tableProvider,
    @required this.draft,
    this.onChanged,
    Key key,
  }) : super(key: key);

  final TableProvider tableProvider;
  ApiProvider get apiProvider => tableProvider.apiProvider;
  final OrdersPostReq draft;
  final Function onChanged;

  @override
  _OrdersPostReqEditFormState createState() => _OrdersPostReqEditFormState();
}

class _OrdersPostReqEditFormState extends State<OrdersPostReqEditForm> {
  static const _insetPadding = const EdgeInsets.symmetric(
    vertical: 24.0,
    horizontal: 80.0,
  );

  final _buyerNameEditing = TextEditingController();
  final _buyerPhoneEditing = TextEditingController();

  final _receiverNameEditing = TextEditingController();
  final _receiverPhoneEditing = TextEditingController();
  final _receiverAddressEditing = TextEditingController();

  final _dateEditing = TextEditingController();
  final _mealEditing = TextEditingController();
  final _cityEditing = TextEditingController();
  final _distEditing = TextEditingController();

  final _adultEditing = TextEditingController();
  final _childEditing = TextEditingController();

  final _commentEditing = TextEditingController();
  final _memoEditing = TextEditingController();

  final _workers = Rx<Workers>(null);
  final date = Rx<DateTime>(null);
  final meal = Rx<TimeOfDay>(null);
  final city = Rx<City>(null);
  final dist = Rx<District>(null);
  final buyerName = ''.obs;
  final buyerPhone = ''.obs;
  final receiverName = ''.obs;
  final receiverPhone = ''.obs;
  final receiverAddress = ''.obs;
  final comment = ''.obs;
  final memo = ''.obs;

  @override
  void initState() {
    initForm();
    super.initState();
  }

  @override
  void didUpdateWidget(OrdersPostReqEditForm oldWidget) {
    refresh();
    super.didUpdateWidget(oldWidget);
  }

  refresh() async {
    //非常智障，但是不把他們停下來真的會有錯，這是照著原先的設計繼續做下去的
    _workers.value?.dispose();
    await initFormFields();
    setupWorkers();
  }

  initForm() async {

    await initFormFields();

    setupWorkers();
  }

  initFormFields() async {
    //這邊嘗試初始化界面
    if (widget.draft != null) {
      //Date
      DateTime mealAtDate = widget.draft.getMealAtLocalDate();
      if (mealAtDate != null) {
        _dateEditing.text = DateFormat('MM/dd').format(mealAtDate);
        date.value = mealAtDate;
      } else {
        //Set date default value to today.
        DateTime now = DateTime.now();
        _dateEditing.text = DateFormat('MM/dd').format(now);
        date.value = DateTime(now.year, now.month, now.day);
      }

      //Meal
      TimeOfDay mealTimeOfDay = widget.draft.getMealLocalTimeOfDay();
      if (mealTimeOfDay != null) {
        _mealEditing.text = timeOfDayFormattedStr(mealTimeOfDay);
        meal.value = mealTimeOfDay;
      } else {
        DateTime now = DateTime.now();
        final ls = await getTimeOfDaysForMealAt(now) ?? <TimeOfDay>[];
        if (ls.length > 0) {
          _mealEditing.text = timeOfDayFormattedStr(ls.first);
          meal.value = ls.first;
          setMealAtTimeOfDayToday(ls.first);
        }
      }

      //receiver name.
      if (widget.draft.receiverName != null) {
        _receiverNameEditing.text = widget.draft.receiverName;
        receiverName.value = widget.draft.receiverName;
        // print('set name [' + widget.ordersPostReq.receiverName + ']');
      }

      //receiver phone
      if (widget.draft.receiverPhone != null) {
        _receiverPhoneEditing.text = widget.draft.receiverPhone;
        receiverPhone.value = widget.draft.receiverPhone;
        // print('set phone [' + widget.ordersPostReq.receiverPhone + ']');
      }

      //receiver address City
      if (widget.draft.receiverCityId != null) {
        // print('set City [' + widget.ordersPostReq.receiverCityId.toString() + ']');
        Iterable<City> cities = await widget.apiProvider.getCities();
        if (cities != null) {
          City matchCity = cities.firstWhere(
              (element) => widget.draft.receiverCityId == element.id,
              orElse: () {
            return null;
          });
          if (matchCity != null) {
            _cityEditing.text = matchCity.name;
            city.value = matchCity;
          }
        }
      } else {
        // print('receiverCityId is null!');
      }

      //receiver address District
      if (widget.draft.receiverCityareaId != null &&
          widget.draft.receiverCityId != null) {
        // print('set District [' + widget.ordersPostReq.receiverCityAreaId.toString() + ']');
        final districts = await widget.apiProvider.getDistricts();
        if (districts != null) {
          final matchDistricts = districts
              .where((element) => widget.draft.receiverCityId == element.cityId)
              .toList();
          if (matchDistricts != null) {
            final matchDistrict = matchDistricts.firstWhere(
                (element) => element.id == widget.draft.receiverCityareaId,
                orElse: () {
              return null;
            });
            if (matchDistrict != null) {
              _distEditing.text = matchDistrict.name;
              dist.value = matchDistrict;
            }
          }
        }
      } else {
        // print('receiverCityAreaId is null!');
      }

      //receiver address
      if (widget.draft.receiverAddress != null) {
        _receiverAddressEditing.text = widget.draft.receiverAddress;
        receiverAddress.value = widget.draft.receiverAddress;
      }

      // buyer name
      if (widget.draft.buyerName != null) {
        _buyerNameEditing.text = widget.draft.buyerName;
        buyerName.value = widget.draft.buyerName;
        // print('set name [' + widget.ordersPostReq.receiverName + ']');
      } else {
        //Auto patch the buyer name from receiver name if it's empty.
        // if (widget.ordersPostReq.receiverName != null) {
        //   widget.ordersPostReq.buyerName = widget.ordersPostReq.receiverName;
        //   _buyerNameController.text = widget.ordersPostReq.receiverName;
        //   buyerName.value = widget.ordersPostReq.receiverName;
        // }
      }

      // buyer phone
      if (widget.draft.buyerPhone != null) {
        _buyerPhoneEditing.text = widget.draft.buyerPhone;
        buyerPhone.value = widget.draft.buyerPhone;
        // print('set name [' + widget.ordersPostReq.receiverName + ']');
      } else {
        //Auto patch the buyer phone from receiver phone if it's empty.
        // if (widget.ordersPostReq.receiverPhone != null) {
        //   widget.ordersPostReq.buyerPhone = widget.ordersPostReq.receiverPhone;
        //   _buyerPhoneController.text = widget.ordersPostReq.receiverPhone;
        //   buyerPhone.value = widget.ordersPostReq.receiverPhone;
        // }
      }

      //Comment.
      if (widget.draft.comment != null) {
        _commentEditing.text = widget.draft.comment;
        comment.value = widget.draft.comment;
      }

      //memo.
      if (widget.draft.memo != null) {
        _memoEditing.text = widget.draft.memo;
        memo.value = widget.draft.memo;
      }
    } else {
      print('Oops! ordersPostReq is null!');
    }

    // adult
    if (widget.draft.adult != null && widget.draft.adult is num) {
      final n = widget.draft.adult;
      this._adultEditing.text = '$n位大人';
    }

    // child
    if (widget.draft.child != null && widget.draft.child is num) {
      final n = widget.draft.child;
      this._childEditing.text = '$n位孩童';
    }
  }

  setupWorkers() {
    final worker0 = ever<DateTime>(this.date, (value) {
      if (value != null) {
        this._dateEditing.text = DateFormat('MM/dd').format(value);
        if (getBrandsType() == BrandsType.BeforeDinner) {
          setMealAtTimeOfDayToday(meal.value);
        } else {
          setMealAtTimeOfDay(value, meal.value);
        }
        widget.onChanged?.call();
      }
    });

    final worker1 = ever<TimeOfDay>(this.meal, (value) {
      if (value != null) {
        this._mealEditing.text = value?.display ?? '';
        if (getBrandsType() == BrandsType.BeforeDinner) {
          setMealAtTimeOfDayToday(value);
        } else {
          setMealAtTimeOfDay(date.value, value);
        }
        widget.onChanged?.call();
      }
    });

    final worker2 = ever<City>(this.city, (value) {
      widget.draft.receiverCityId = value.id;
      this._cityEditing.text = value.name;
      widget.onChanged?.call();
    });

    final worker3 = ever<District>(this.dist, (value) {
      widget.draft.receiverCityareaId = value?.id ?? -1;
      this._distEditing.text = value?.name ?? '';
      widget.onChanged?.call();
    });

    final worker31 = ever<String>(this.buyerName, (value) {
      widget.draft.buyerName = value;
      widget.onChanged?.call();
    });

    final worker32 = ever<String>(this.buyerPhone, (value) {
      widget.draft.buyerPhone = value;
      widget.onChanged?.call();
    });

    final worker4 = ever<String>(this.receiverName, (value) {
      widget.draft.receiverName = value;
      widget.onChanged?.call();
    });

    final worker5 = ever<String>(this.receiverPhone, (value) {
      widget.draft.receiverPhone = value;
      widget.onChanged?.call();
    });

    final worker6 = ever<String>(this.receiverAddress, (value) {
      widget.draft.receiverAddress = value;
      widget.onChanged?.call();
    });

    final worker7 = ever<String>(this.comment, (value) {
      widget.draft.comment = value;
      widget.onChanged?.call();
    });

    final worker71 = ever<String>(this.memo, (value) {
      widget.draft.memo = value;
      widget.onChanged?.call();
    });

    this._workers.value = Workers([
      worker0,
      worker1,
      worker2,
      worker3,
      worker31,
      worker32,
      worker4,
      worker5,
      worker6,
      worker7,
      worker71,
    ]);
  }

  @override
  void dispose() {
    _workers.value?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    int orderType = getOrderType();
    List<Widget> columnChildren = [];
    //Edit table button.
    //Only for type 0, 1
    if (shouldSelectTable()) {
      columnChildren.add(editTable());
    }

    //Edit meal at time.
    if (ORDER_TYPE_DINNER.contains(orderType.orderType)) {
      // 時間
      columnChildren.add(editMealAtTime());
    }

    if ([OrderType.DinnerHere, OrderType.DinnerOrder]
        .contains(orderType.orderType)) {
      // 人數
      columnChildren.add(editNumOfMeals());
    }

    //Do we need a Delivery arrive time?
    if ([OrderType.DinnerDelivery, OrderType.RetailDelivery]
        .contains(orderType.orderType)) {
      // 編輯外送地址
      columnChildren.add(editDeliveryForm());
    }

    if ([OrderType.DinnerToGo].contains(orderType.orderType)) {
      // memo: 顧客備註，顧客看得到
      columnChildren.add(editMemo());
    }

    // comment: 店家備註 (顧客看不到)
    columnChildren.add(editComment());

    columnChildren.add(SizedBox(height: 12.0));

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: columnChildren,
    );
  }

  //--

  Widget editTable() {
    return InkWell(
      child: Container(
        height: kMinInteractiveDimension,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey, width: 0.5),
          color: Colors.white,
        ),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Expanded(child: Container()),
            Center(
                child: Text(
              selectedTableDisplayName(),
              style: Get.textTheme.headline6.copyWith(color: kColorPrimary),
            )),
            // Expanded(child: Container()),
            Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Icon(Icons.edit),
              ),
            ),
          ],
        ),
      ),
      onTap: () async {
        //Open the tables selection page.
        // TODO: 置換參數，使用結構，移除 Tuple3
        Tuple3<int, int, String> resultPartitionTable = await Get.toNamed(
            Routes.TABLES_SELECTION,
            arguments: TablesSelectionArgs(
                table1Id: widget.draft.table1Id,
                table2Id: widget.draft.table2Id)) as Tuple3<int, int, String>;

        if (resultPartitionTable != null) {
          // print('Got resultPartitionTable [' + resultPartitionTable.item1.toString() + '][' + resultPartitionTable.item2.toString() + ']');
          //設定回傳值給 ordersPostReq.
          widget.draft.table1Id = resultPartitionTable.item1;
          widget.draft.table2Id = resultPartitionTable.item2;
          setState(() {});
          widget.onChanged?.call();
        }
      },
    );
  }

  // Widget get buyerName {
  //   return Column(
  //     mainAxisSize: MainAxisSize.min,
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       const Text(
  //         '訂購者姓名：',
  //         style: const TextStyle(
  //           fontSize: 14,
  //           color: const Color(0xff222222),
  //         ),
  //         textAlign: TextAlign.left,
  //       ).paddingOnly(
  //         bottom: 4.0,
  //       ),
  //       _TextFormField(
  //         controller: this._buyerNameController,
  //         autoValidateMode: AutovalidateMode.always,
  //         onChanged: this._buyerName,
  //         inputFormatters: [
  //           FilteringTextInputFormatter.singleLineFormatter,
  //         ],
  //         validator: (value) => null,
  //       ),
  //     ],
  //   );
  // }
  //
  // Widget get buyerPhone {
  //   return Column(
  //     mainAxisSize: MainAxisSize.min,
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       const Text(
  //         '訂購者電話：',
  //         style: const TextStyle(
  //           fontSize: 14,
  //           color: const Color(0xff222222),
  //         ),
  //         textAlign: TextAlign.left,
  //       ).paddingOnly(
  //         bottom: 4.0,
  //       ),
  //       _TextFormField(
  //         controller: this._buyerPhoneController,
  //         autoValidateMode: AutovalidateMode.always,
  //         onChanged: this._buyerPhone,
  //         keyboardType: TextInputType.phone,
  //         inputFormatters: [
  //           FilteringTextInputFormatter.singleLineFormatter,
  //           FilteringTextInputFormatter.digitsOnly,
  //         ],
  //         validator: (value) => null,
  //       ),
  //     ],
  //   );
  // }

  //編輯用餐人數
  Widget editNumOfMeals() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '人數:',
          style: Get.textTheme.subtitle1.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(
          height: 8,
        ),
        Row(
          children: [
            Expanded(
              child: _TextFormField(
                readOnly: true,
                validator: (value) {
                  // if (this.meal.value == null) {
                  //   return '選擇日期';
                  // }
                  return null;
                },
                onTap: () {
                  this._showAdultPicker();
                },
                controller: this._adultEditing,
                suffixIcon: Icon(Icons.expand_more),
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            //Drop down for select meal at time.
            Expanded(
              child: _TextFormField(
                readOnly: true,
                validator: (value) {
                  // if (this.meal.value == null) {
                  //   return '選擇時間';
                  // }
                  return null;
                },
                onTap: () {
                  this._showChildPicker();
                },
                controller: this._childEditing,
                suffixIcon: Icon(Icons.expand_more),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 顯示大人選擇器 (0..20)
  void _showAdultPicker() {
    ListView.builder(
      // TODO: magic number
      itemCount: 21,
      itemBuilder: (context, index) {
        return ListTile(
          tileColor: Colors.white,
          onTap: () {
            Get.back(result: index);
          },
          title: Text('$index位大人'),
        );
      },
    )
        .dialog(
      insetPadding: _insetPadding,
      barrierDismissible: true,
    )
        .then(
      (value) {
        if (value != null) {
          widget.draft.adult = value;
          _adultEditing.text = '$value位大人';
          widget.onChanged?.call();
        }
      },
    );
  }

  // 顯示小孩選擇器 (0..10)
  void _showChildPicker() {
    ListView.builder(
      // TODO: magic number
      itemCount: 11,
      itemBuilder: (context, index) {
        return ListTile(
          tileColor: Colors.white,
          onTap: () {
            Get.back(result: index);
          },
          title: Text('$index位孩童'),
        );
      },
    )
        .dialog(
      insetPadding: _insetPadding,
      barrierDismissible: true,
    )
        .then(
      (value) {
        if (value != null) {
          widget.draft.child = value;
          _childEditing.text = '$value位孩童';
          widget.onChanged?.call();
        }
      },
    );
  }

  //編輯用餐時間
  Widget editMealAtTime() {
    String labelText = widget.draft.type == 0 ? '用餐時間: ' : '取餐時間: ';

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            labelText,
            style:
                Get.textTheme.subtitle1.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 8,
          ),

          Row(
            children: [
              //Drop down for select meal at time.
              Expanded(
                child: _TextFormField(
                  readOnly: true,
                  validator: (value) {
                    // if (this.meal.value == null) {
                    //   return '選擇日期';
                    // }
                    return null;
                  },
                  onTap: () {
                    this._showDatePicker();
                  },
                  controller: this._dateEditing,
                  suffixIcon: Icon(Icons.keyboard_arrow_down),
                ),
              ),

              SizedBox(
                width: 8,
              ),

              //Drop down for select meal at time.
              Expanded(
                child: _TextFormField(
                  readOnly: true,
                  validator: (value) {
                    // if (this.meal.value == null) {
                    //   return '選擇時間';
                    // }
                    return null;
                  },
                  onTap: () {
                    this._showTimePicker(date.value);
                  },
                  controller: this._mealEditing,
                  suffixIcon: Icon(Icons.keyboard_arrow_down),
                ),
              ),
            ],
          ),
          // _DecoratedBox(
          //   child: _TextButton(
          //     text: meal.value?.display,
          //     onPressed: () {
          //       this._showTimePicker();
          //     },
          //   ),
          // ),
          // Container(
          //   padding: EdgeInsets.symmetric(horizontal: 16),
          //   height: kMinInteractiveDimension,
          //   decoration: BoxDecoration(
          //     border: Border.all(color: Colors.grey, width: 0.5),
          //     color: Colors.white,
          //   ),
          //   child: DropdownButton<TimeOfDay>(
          //     isExpanded: true,
          //     underline: Container(),
          //     value: getMealAtTimeOfDay(),
          //     onChanged: (TimeOfDay timeOfDay) {
          //       setMealAtTimeOfDay(timeOfDay);
          //     },
          //     items: getTimeOfDaysForMealAt()
          //         .map<DropdownMenuItem<TimeOfDay>>((TimeOfDay timeOfDay) {
          //       return DropdownMenuItem<TimeOfDay>(
          //         value: timeOfDay,
          //         child: Text(
          //           timeOfDayFormattedStr(timeOfDay),
          //           style: Get.textTheme.subtitle1,
          //         ),
          //       );
          //     }).toList(),
          //     hint: Text(
          //       '選擇時間',
          //       style: Get.textTheme.subtitle1.copyWith(color: Colors.grey),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  //編輯 comment.
  Widget editComment() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              text: '店家備註:',
              style: Get.textTheme.subtitle1.copyWith(
                fontWeight: FontWeight.bold,
              ),
              children: [
                TextSpan(
                  text: '客戶看不到店家備註內容',
                  style: Get.textTheme.subtitle1.copyWith(
                    color: OKColor.Gray66,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 8,
          ),
          Focus(
            child: RoundedShadowContainer(
              child: TextField(
                controller: _commentEditing,
                decoration: InputDecoration(
                  hintText: '請輸入備註',
                  hintStyle:
                      Get.textTheme.bodyText1.copyWith(color: Colors.grey),
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                  isDense: true,
                ),
                minLines: 5,
                maxLines: 5,
                onChanged: comment,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 編輯 memo
  Widget editMemo() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '顧客備註:',
          style: Get.textTheme.subtitle1.copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(
          height: 8,
        ),
        Focus(
          child: RoundedShadowContainer(
            child: TextField(
              controller: _memoEditing,
              decoration: InputDecoration(
                hintText: '請輸入備註',
                hintStyle: Get.textTheme.bodyText1.copyWith(color: Colors.grey),
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                isDense: true,
              ),
              minLines: 5,
              maxLines: 5,
              onChanged: memo,
            ),
          ),
        ),
      ],
    );
  }

  //--

  void _showTimePicker(DateTime date) async {
    // final ls = <TimeOfDay>[];
    final ls = await getTimeOfDaysForMealAt(date) ?? <TimeOfDay>[];
    if (ls.length > 0) {
      _showTimePicker2(date);
    } else {
      DialogGeneral(DialogArgs(
        contentIcon: DialogContentIcon.Alert,
        content: Center(child: Text('沒有可選擇時段')),
        mainButtonText: '確定',
      )).dialog(
        barrierDismissible: false,
      );
    }
  }

  void _showTimePicker2(DateTime date) async {
    final ls = await getTimeOfDaysForMealAt(date) ?? <TimeOfDay>[];
    ListView.builder(
      itemCount: ls.length,
      itemBuilder: (context, index) {
        final data = ls.elementAt(index);
        final text = timeOfDayFormattedStr(data);
        return ListTile(
          tileColor: Colors.white,
          onTap: () {
            Get.back(result: data);
          },
          title: Text(text ?? ''),
        );
      },
    )
        .dialog(
      insetPadding: _insetPadding,
      barrierDismissible: true,
    )
        .then(
      (value) {
        if (value != null) {
          meal.value = value;
        }
      },
    );
  }

  void _showDatePicker() async {
    List<DateTime> dates = await getDateList() ?? <DateTime>[];
    ListView.builder(
      itemCount: dates.length,
      itemBuilder: (context, index) {
        final data = dates.elementAt(index);
        final text = DateFormat('MM/dd').format(data);
        return ListTile(
          tileColor: Colors.white,
          onTap: () {
            Get.back(result: data);
          },
          title: Text(text ?? ''),
        );
      },
    )
        .dialog(
      insetPadding: _insetPadding,
      barrierDismissible: true,
    )
        .then(
      (value) {
        if (value != null) {
          date.value = value;
        }
      },
    );
  }

  Widget editDeliveryForm() {
    List<Widget> columnChildren = [];
    columnChildren.add(Text(
      '收件人姓名：',
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff222222),
      ),
      textAlign: TextAlign.left,
    ).paddingOnly(
      bottom: 4.0,
    ));

    columnChildren.add(_TextFormField(
      controller: _receiverNameEditing,
      onChanged: this.receiverName,
      inputFormatters: [
        FilteringTextInputFormatter.singleLineFormatter,
      ],
      validator: (value) {
        if (receiverName.value.isEmpty) {
          return '必填項目';
        }
        return null;
      },
    ));

    columnChildren.add(SizedBox(
      height: kPadding,
    ));

    columnChildren.add(Text(
      '收件人電話：',
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff222222),
      ),
      textAlign: TextAlign.left,
    ).paddingOnly(
      bottom: 4.0,
    ));

    columnChildren.add(_TextFormField(
      controller: _receiverPhoneEditing,
      keyboardType: TextInputType.phone,
      onChanged: this.receiverPhone,
      validator: (value) {
        if (receiverPhone.value.isEmpty) {
          return '必填項目';
        }
        return null;
      },
      inputFormatters: [
        FilteringTextInputFormatter.singleLineFormatter,
        FilteringTextInputFormatter.digitsOnly,
      ],
    ));

    columnChildren.add(SizedBox(
      height: kPadding,
    ));

    columnChildren.add(Text(
      '收件人地址：',
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff222222),
      ),
      textAlign: TextAlign.left,
    ).paddingOnly(
      bottom: 4.0,
    ));

    columnChildren.add(_District(
      leftController: _cityEditing,
      rightController: _distEditing,
      leftText: city.value?.name ?? '',
      rightText: dist.value?.name ?? '',
      leftPressed: () {
        _showCities();
      },
      rightPressed: () {
        _showDistricts();
      },
      leftValidator: (value) {
        if (city.value == null) {
          return '必填項目';
        }
        return null;
      },
      rightValidator: (value) {
        if (dist.value == null) {
          return '必填項目';
        }
        return null;
      },
    ).paddingOnly(
      bottom: 8.0,
    ));

    columnChildren.add(_TextFormField(
      controller: _receiverAddressEditing,
      onChanged: this.receiverAddress,
      inputFormatters: [
        FilteringTextInputFormatter.singleLineFormatter,
      ],
      validator: (value) {
        if (receiverAddress.value.isEmpty) {
          return '必填項目';
        }
        return null;
      },
    ));

    columnChildren.add(SizedBox(
      height: kPadding,
    ));

    int orderType = getOrderType();
    if (orderType == MEAL_ORDER_TYPE_TAKE_AWAY_OR_FETCH_AWAY ||
        orderType == MEAL_ORDER_TYPE_DELIVERY ||
        orderType == RETAIL_ORDER_TYPE_HOME_DELIVERY ||
        orderType == RETAIL_ORDER_TYPE_CONVENIENCE_STORE) {
      columnChildren.add(Text(
        '訂購人姓名：',
        style: const TextStyle(
          fontSize: 14,
          color: const Color(0xff222222),
        ),
        textAlign: TextAlign.left,
      ).paddingOnly(
        bottom: 4.0,
      ));

      columnChildren.add(_TextFormField(
        controller: _buyerNameEditing,
        onChanged: this.buyerName,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
        ],
        validator: (value) {
          if (buyerName.value.isEmpty) {
            return '必填項目';
          }
          return null;
        },
      ));

      columnChildren.add(SizedBox(
        height: kPadding,
      ));

      columnChildren.add(Text(
        '訂購人電話：',
        style: const TextStyle(
          fontSize: 14,
          color: const Color(0xff222222),
        ),
        textAlign: TextAlign.left,
      ).paddingOnly(
        bottom: 4.0,
      ));

      columnChildren.add(_TextFormField(
        controller: _buyerPhoneEditing,
        keyboardType: TextInputType.phone,
        onChanged: this.buyerPhone,
        validator: (value) {
          if (buyerPhone.value.isEmpty) {
            return '必填項目';
          }
          return null;
        },
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
      ));
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: columnChildren,
    );
  }

  void _showCities() {
    FutureProgress<Iterable<City>>(
      future: widget.apiProvider.getCities(),
      onData: (value) {
        return ListView.builder(
          itemCount: value.length,
          itemBuilder: (context, index) {
            final data = value.elementAt(index);
            return ListTile(
              tileColor: Colors.white,
              onTap: () {
                Get.back(result: data);
              },
              title: Text(data.name ?? ''),
            );
          },
        );
      },
    )
        .dialog<City>(
      insetPadding: _insetPadding,
      barrierDismissible: true,
    )
        .then(
      (value) {
        if (value != null) {
          this.city.value = value;
          this.dist.value = null;
        }
      },
    );
  }

  void _showDistricts() {
    final cityId = this.city.value?.id ?? -1;
    if (cityId < 0) {
      DialogGeneral(DialogArgs(
        // header: Text('先選擇城市'),
        content: Center(child: Text('先選擇城市')),
        mainButtonText: '確定',
      )).dialog();
    } else {
      FutureProgress<Iterable<District>>(
        future: widget.apiProvider.getDistricts().then(
          (value) {
            final cityId = this.city.value?.id ?? -1;
            return value.where((element) => cityId == element.cityId);
          },
        ),
        onData: (value) {
          return ListView.builder(
            itemCount: value.length,
            itemBuilder: (context, index) {
              final data = value.elementAt(index);
              return ListTile(
                tileColor: Colors.white,
                onTap: () {
                  Get.back(result: data);
                },
                title: Text(data.name ?? ''),
              );
            },
          );
        },
      )
          .dialog<District>(
        insetPadding: _insetPadding,
        barrierDismissible: true,
      )
          .then(
        (value) {
          if (value != null) {
            dist.value = value;
          }
        },
      );
    }
  }

  //--

  //取得當前選取桌面按鈕上的顯示文字
  String selectedTableDisplayName() {
    if (widget.draft != null &&
        widget.draft.table1Id != null &&
        widget.draft.table2Id != null) {
      return widget.tableProvider.getDisplayName(
          widget.draft.table1Id, widget.draft.table2Id);
    }
    //在無資料或者沒有選擇好的狀況下自然只會回預設值。
    return '桌號';
  }

  //是否已經選擇了合法的桌號
  bool hasSelectTable() {
    if (widget.draft != null) {
      return widget.draft.hasSelectLegalTable();
    }
    return false;
  }

  //Try return the checkout type setting.
  BrandsType getBrandsType() {
    return widget.apiProvider.prefProvider.setting.data.other.getBrandsType();
  }

  //訂單類型
  getOrderType() {
    if (widget.draft != null && widget.draft.type != null) {
      return widget.draft.type;
    }
    return 0;
  }

  //當前的訂單 type 是否需要選擇桌號?
  bool shouldSelectTable() {
    if (widget.draft != null) {
      return widget.draft.shouldSelectTable();
    }
    return false;
  }

  //設定訂單類型
  setOrderType(int type) {
    if (widget.draft != null) {
      setState(() {
        widget.draft.type = type;
      });
    }
  }

  //This will return a date list for 30 days from now.
  Future<List<DateTime>> getDateList() async {
    final settings = await widget.apiProvider.getSetting();

    final settingWeekDays = settings.data?.hours?.week ?? <SettingWeekDay>[];
    List<DateTime> returnList = [];
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day);
    for (int i = 0; i < 30; i++) {
      DateTime date = today.add(Duration(days: i));
      final weekDay =
          settingWeekDays.firstWhere((weekDay) => date.weekday == weekDay.day);
      if (weekDay.status == 1) {
        returnList.add(date);
      }
    }
    return returnList;
  }

  //This will return the TimeOfDay after now() and separated by 15 minutes.
  Future<List<TimeOfDay>> getTimeOfDaysForMealAt(DateTime date) async {
    List<TimeOfDay> returnList = [];

    final now = DateTime.now();
    // 取得設定
    final settings = await widget.apiProvider.getSetting();

    // 取得營業時間
    final settingWeekDays = settings.data?.hours?.week ?? <SettingWeekDay>[];

    // print('settingWeekDays length: ' + settingWeekDays.length.toString() + ' | date [' + date.toString() + ']');
    // print('settingWeekDays[0] hours length: ' + settingWeekDays[0].hours.length.toString());

    if (settingWeekDays.length == 0) {
      print('Oops! settingWeekDays is Empty!');
      //我不知道為什麼，可能是根本沒填資料。
      return [];
    }

    // 取得今日營業時間
    final weekDay =
        settingWeekDays.firstWhere((weekDay) => date.weekday == weekDay.day);

    // if (weekDay == null) {
    //   print('Oops! weekDay is null!');
    // } else {
    //   print('got weekday: ' + weekDay.day.toString());
    // }

    //合法分
    List<int> listMinutes = [0, 15, 30, 45];

    //判斷date是否是今天
    if (now.year == date.year &&
        now.month == date.month &&
        now.day == date.day) {
      //是今天
      int hourNow = now.hour;
      int minuteNow = now.minute;

      //Loop through 24 hours
      for (int h = 0; h < 24; h++) {
        if (h >= hourNow) {
          //There are legal items in this hour. Gonna add them here.
          for (int i = 0; i < listMinutes.length; ++i) {
            if (h > hourNow || listMinutes[i] > minuteNow) {
              // print('add hour [' + h.toString() + '] minute [' + listMinutes[i].toString() + ']');
              //This is a valid time for list.
              final target = TimeOfDay(hour: h, minute: listMinutes[i]);
              if (weekDay?.inRange(target) ?? false) {
                returnList.add(TimeOfDay(hour: h, minute: listMinutes[i]));
              }
            }
          }
        }
      }
    } else if (date.isAfter(now)) {
      //是今天過後
      //Loop through 24 hours
      for (int h = 0; h < 24; h++) {
        //There are legal items in this hour. Gonna add them here.
        for (int i = 0; i < listMinutes.length; ++i) {
          // print('add hour [' + h.toString() + '] minute [' + listMinutes[i].toString() + ']');
          //This is a valid time for list.
          final target = TimeOfDay(hour: h, minute: listMinutes[i]);
          if (weekDay?.inRange(target) ?? false) {
            returnList.add(TimeOfDay(hour: h, minute: listMinutes[i]));
          }
        }
      }
    } else {
      //是今天之前
      return [];
    }

    return returnList;
  }

  //取得用餐時間 TimeOfDay，可能會回傳null
  TimeOfDay getMealAtTimeOfDay() {
    if (widget.draft != null) {
      return tryParseToTimeOfDay(widget.draft.mealAt);
    }
    return null;
  }

  //設定用餐時間，事實上是存成字串
  setMealAtTimeOfDayToday(TimeOfDay timeOfDay) {
    if (widget.draft != null && timeOfDay != null) {
      final utc = timeOfDay.withToday.toUtc();
      final time = DateFormat('yyyy-MM-dd HH:mm:ss').format(utc);
      // print('setMealAtTimeOfDayToday: ' + time);
      widget.draft.mealAt = time;
      setState(() {});
    }
  }

  //設定用餐時間，事實上是存成字串
  setMealAtTimeOfDay(DateTime date, TimeOfDay timeOfDay) {
    if (widget.draft != null && date != null && timeOfDay != null) {
      final utc = timeOfDay.withDate(date).toUtc();
      final time = DateFormat('yyyy-MM-dd HH:mm:ss').format(utc);
      // print('setMealAtTimeOfDay: ' + time);
      widget.draft.mealAt = time;
      setState(() {});
    }
  }

  String timeOfDayFormattedStr(TimeOfDay timeOfDay) {
    if (timeOfDay != null) {
      return timeOfDay.hour.toString().padLeft(2, '0') +
          ':' +
          timeOfDay.minute.toString().padLeft(2, '0');
    }
    return '';
  }

  //The format should be like this: '01:30'.
  //Possible return null if parse failed.
  TimeOfDay tryParseToTimeOfDay(String timeStr) {
    if (timeStr != null) {
      DateTime parsedDateTime =
          DateFormat('yyyy-MM-dd HH:mm:ss').parse(timeStr);
      if (parsedDateTime != null) {
        return TimeOfDay.fromDateTime(parsedDateTime);
      }
    }
    return null;
  }
}

class _District extends StatelessWidget {
  final String leftText;
  final String rightText;
  final Function leftPressed;
  final Function rightPressed;
  final ValueChanged<String> leftValidator;
  final ValueChanged<String> rightValidator;
  final TextEditingController leftController;
  final TextEditingController rightController;

  const _District({
    Key key,
    this.leftText,
    this.rightText,
    this.leftPressed,
    this.rightPressed,
    this.leftValidator,
    this.rightValidator,
    this.leftController,
    this.rightController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _TextFormField(
            readOnly: true,
            validator: this.leftValidator,
            onTap: this.leftPressed,
            controller: this.leftController,
            suffixIcon: Icon(Icons.keyboard_arrow_down),
          ),
        ),
        const SizedBox(
          width: 6.0,
        ),
        Expanded(
          child: _TextFormField(
            readOnly: true,
            validator: this.rightValidator,
            onTap: this.rightPressed,
            controller: this.rightController,
            suffixIcon: Icon(Icons.keyboard_arrow_down),
          ),
        ),
      ],
    );
  }
}

class _TextButton extends TextButton {
  _TextButton({
    Key key,
    String text,
    Function onPressed,
  }) : super(
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
          ),
          onPressed: onPressed,
          key: key,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  // '台北市',
                  text ?? '',
                  style: const TextStyle(
                    fontSize: 15,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.left,
                ).paddingSymmetric(
                  horizontal: kPadding,
                ),
              ),
              Icon(Icons.keyboard_arrow_down),
              // ExpandIcon(
              //   onPressed: onExpandPressed,
              // ),
            ],
          ),
        );
}

class _TextFormField extends TextFormField {
  _TextFormField({
    Key key,
    bool readOnly,
    TextInputType keyboardType,
    ValueChanged<String> onChanged,
    ValueChanged<String> validator,
    Function onTap,
    Iterable<TextInputFormatter> inputFormatters,
    TextEditingController controller,
    Widget suffixIcon,
    AutovalidateMode autoValidateMode,
  }) : super(
          key: key,
          autovalidateMode: autoValidateMode ?? AutovalidateMode.disabled,
          onTap: onTap,
          readOnly: readOnly ?? false,
          onChanged: onChanged,
          validator: validator,
          keyboardType: keyboardType,
          controller: controller,
          inputFormatters: inputFormatters ??
              [
                FilteringTextInputFormatter.singleLineFormatter,
              ],
          decoration: InputDecoration(
            suffixIcon: suffixIcon,
            // border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12.0,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(2.0),
              borderSide: BorderSide.none,
            ),
            //   // isCollapsed: true,
            fillColor: Colors.white,
            filled: true,
          ),
          style: const TextStyle(
            fontSize: 15,
            color: Colors.black,
          ),
        );
}

class _DecoratedBox extends StatelessWidget {
  final Widget child;
  const _DecoratedBox({
    Key key,
    @required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: const BoxDecoration(
        borderRadius: const BorderRadius.all(const Radius.circular(2.0)),
        color: Colors.white,
        boxShadow: const [
          const BoxShadow(
            color: const Color(0x33000000),
            offset: const Offset(0.0, 1.0),
            blurRadius: 0.0,
          ),
        ],
      ),
      child: this.child ?? SizedBox(),
    );
  }
}
