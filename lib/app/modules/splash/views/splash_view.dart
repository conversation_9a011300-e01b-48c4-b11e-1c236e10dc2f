import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/background.dart';
import 'package:sizer/sizer.dart';

import '../controllers/splash_controller.dart';

class SplashView extends GetView<SplashController> {
  const SplashView({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(
        body: Background(
          background: SvgPicture.asset(
            'assets/images/bg_login.svg',
            fit: BoxFit.fitWidth,
            width: double.infinity,
            height: double.infinity,
            alignment: Alignment.topCenter,
          ),
          child: controller.obx((state) {
            return Center(child: _body());
          }),
        ),
      ),
    );
  }

  Widget _body() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    // 应用Logo
    yield Container(
      width: 30.w,
      height: 30.w,
      child: SvgPicture.asset(
        'assets/images/app_logo.svg',
        color: Colors.white,
      ),
    );
    // 加载指示器
    yield SizedBox(
      width: 8.w,
      height: 8.w,
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        strokeWidth: 2.0,
      ),
    );
  }
}
