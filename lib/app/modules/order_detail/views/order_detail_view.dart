import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart' hide Svg;
import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/bottom_wrapper.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/invoice_widget.dart';
import 'package:muyipork/app/components/label_value.dart';
import 'package:muyipork/app/components/line_pusher.dart';
import 'package:muyipork/app/components/line_tag.dart';
import 'package:muyipork/app/components/member_avatar.dart';
import 'package:muyipork/app/components/orders_post_req_edit_form.dart';
import 'package:muyipork/app/components/radio_button.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/coupon_item.dart';
import 'package:muyipork/app/components/underline_divider.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/app/data/models/other/order_detail.dart';
import 'package:muyipork/app/data/models/req/orders_post_req.dart';
import 'package:muyipork/app/data/models/res/orders_get_res.dart';
import 'package:muyipork/app/data/models/res/order_root.dart';
import 'package:muyipork/app/modules/orders_adjust/controllers/orders_adjust_controller.dart';
import 'package:muyipork/app/modules/receipt_sticker_printing/controllers/receipt_sticker_printing_controller.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:screenshot/screenshot.dart';

import '../controllers/order_detail_controller.dart';

enum Print {
  None,
  Copy,
  Invoice, // 發票
  ReceiptLite, // 消費明細
  ReceiptItem, // 餐點明細
  Sticker, // 工作單
  PostInvoice, // 上傳發票
  PostInvoiceEdit, // 作廢發票
}

extension ExtensionPrint on Print {
  String getName(StoreType storeType) {
    switch (this) {
      case Print.Invoice: // 發票
        return '補印發票';
      case Print.ReceiptLite: // 消費明細
        return '消費明細';
      case Print.ReceiptItem: // 商品明細
        switch (storeType) {
          case StoreType.Dinner:
            return '餐點明細';
          case StoreType.Retail:
            return '商品明細';
          default:
            return '';
        }
        return '';
      case Print.Sticker: // 工作單
        return '工作單';
      case Print.PostInvoice: // 上傳發票
        return '上傳發票';
      case Print.PostInvoiceEdit: // 作廢發票
        return '作廢發票';
      default:
    }
    return '';
  }
}

class OrderDetailView extends GetView<OrderDetailController> {
  // @override
  // final String tag;

  const OrderDetailView({
    Key key,
  }) : super(key: key);

  Iterable<Print> _moreActions() sync* {
    // 發票
    if (controller.printInvoiceEnabled == true) {
      yield Print.Invoice;
    }
    // 消費明細
    if (controller.data.data.status.orderStatus.isFinished == true) {
      yield Print.ReceiptLite;
    }
    // 餐點明細(商品明細)
    yield Print.ReceiptItem;
    // 工作單
    yield Print.Sticker;
    // 上傳發票
    // yield Print.PostInvoice;
    // 作廢發票
    // yield Print.PostInvoiceEdit;
  }

  Future<Print> _showPrintPicker() {
    final completer = Completer<Print>();
    final selected = Print.None.obs;
    final storeType = controller.data.data.orderType.storeType;
    DialogGeneral(
      DialogArgs(
        header: DialogGeneral.titleText(storeType.isDinner ? '請選擇' : '列印'),
        mainButtonText: '確認',
        secondaryButtonText: '取消',
        onMainButtonPress: () {
          completer.complete(selected.value);
        },
        content: Center(
          child: Obx(() {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: _moreActions().map((e) {
                return RadioButton<Print>(
                  titleText: e.getName(storeType),
                  value: e,
                  groupValue: selected.value,
                  onChanged: selected,
                );
              }).toList(growable: false),
            );
          }),
        ),
      ),
    ).dialog();
    return completer.future;
  }

  Future<void> _showMore() async {
    try {
      switch (await _showPrintPicker()) {
        case Print.Invoice: // 發票
          await controller.printInvoice();
          break;
        case Print.ReceiptLite: // 消費明細
          await controller.printReceiptLite(_genReceipt());
          break;
        case Print.ReceiptItem: // 商品明細
          await controller.printReceiptItems(_genReceipt());
          break;
        case Print.Sticker: // 工作單
          await _showPrintPreview();
          break;
        case Print.PostInvoice: // 上傳發票
          await controller.uploadInvoice();
          break;
        case Print.PostInvoiceEdit: // 作廢發票
          await controller.uploadCancel();
          break;
        default:
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Receipt _genReceipt() {
    final brandsInfo = controller.prefProvider.brandsInfo;
    final jwt = controller.prefProvider.jwt;
    return controller.data.asReceipt(
      storeName: brandsInfo.name,
      userName: jwt.name,
    );
  }

  ///
  /// 開啟列印頁面
  ///
  Future<void> _showPrintPreview() async {
    await Get.toNamed(
      Routes.RECEIPT_STICKER_PRINTING,
      parameters: {
        Keys.Tag: '${controller.data.id}',
      },
      arguments: PrintingArgs(
        printingPageSetting: PrintingPageSetting.StickerOnly,
        orderId: controller.data.id,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.focusScope.unfocus(),
      child: StandardPage(
        actions: _actions().toList(growable: false),
        titleText: '訂單詳情',
        child: controller.obx((state) {
          return BottomWidgetPage(
            child: Stack(
              alignment: Alignment.topCenter,
              children: _children().toList(growable: false),
            ),
            bottom: BottomWrapper(
              child: Obx(() => _bottomBar()),
              padding: EdgeInsets.zero,
            ),
          );
        }),
      ),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield Obx(() {
      return Visibility(
        visible: controller.data != null,
        child: _printerButton(),
      );
    });
  }

  Widget _printerButton() {
    return TextButton(
      style: TextButton.styleFrom(
        side: const BorderSide(
          color: Colors.white,
        ),
        shape: const StadiumBorder(),
        padding: kChipPadding,
        minimumSize: Size.zero,
      ),
      child: Text(
        // isDinner ? '更多' : '列印',
        '更多',
        style: const TextStyle(
          fontSize: 16,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
      onPressed: _showMore,
    ).paddingSymmetric(
      vertical: 12.0,
      horizontal: 8.0,
    );
  }

  Iterable<Widget> _children() sync* {
    // 發票截圖
    yield Obx(() {
      if (controller.invoice != null) {
        final ret = Screenshot(
          controller: controller.invoiceScreenshotController,
          child: InvoiceWidget(data: controller.invoice),
        );
        controller.widgetUpdater.complete();
        return ret;
      }
      return const SizedBox();
    });
    // 國防布
    yield const ColoredBox(
      color: kColorBackground,
      child: SizedBox.expand(),
    );
    // 使用者實際看到的預覽介面
    yield Column(
      mainAxisSize: MainAxisSize.min,
      children: _mainChildren().toList(growable: false),
    );
  }

  ///
  /// 最上方狀態標題
  ///
  Widget _header() {
    final data = controller.data;
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: OKColor.GrayF7,
        borderRadius: BorderRadius.vertical(
          top: kRadiusCircular,
        ),
      ),
      // color: Colors.transparent,
      child: ListTile(
        contentPadding: kContentPadding,
        title: LabelValue(
          labelText: '主訂單編號：',
          // valueText: 'line2021098765',
          valueText: data?.data?.orderNumber ?? '',
        ),
        trailing: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: const Color(0xfff0f0f0),
            side: BorderSide.none,
            shape: const StadiumBorder(),
            padding: Constants.chipPadding,
            minimumSize: Size.zero,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                // '訂單完成',
                data?.data?.displayStatus ?? '',
                style: TextStyle(
                  fontSize: 16,
                  color: data?.data?.orderStatus?.color ?? OKColor.Gray33,
                ),
                textAlign: TextAlign.center,
              ),
              Visibility(
                visible: data?.statusCompleted ?? false,
                child: const Icon(
                  Icons.keyboard_arrow_down,
                ),
              ),
            ],
          ),
          onPressed: data.statusCompleted ? _onStatusButtonPressed : null,
        ),
      ),
    );
  }

  ///
  /// 變更訂單狀態按下後
  ///
  Future<void> _onStatusButtonPressed() async {
    try {
      final pickStatus = await _showReject();
      if (pickStatus == OrderStatus.Rejection) {
        final button = await _showConfirm();
        if (button == Button.Positive) {
          FutureProgress<num>(
            future: controller.refund(),
          ).dialog<num>();
        }
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  static const _rejectActions = [
    OrderStatus.Completed,
    OrderStatus.Rejection,
  ];

  Future<OrderStatus> _showReject() {
    final completer = Completer<OrderStatus>();
    final selected = _rejectActions.elementAt(0).obs;
    DialogGeneral(DialogArgs(
      header: Text(
        '修改訂單狀態',
        style: const TextStyle(
          fontSize: 20,
          color: OKColor.Gray33,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
      mainButtonText: '確認',
      secondaryButtonText: '取消',
      onMainButtonPress: () {
        completer.complete(selected.value);
      },
      content: Obx(() {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: _rejectActions.map((e) {
            return RadioListTile<OrderStatus>(
              contentPadding: kContentPadding,
              title: Text(
                e.name ?? '',
                style: const TextStyle(
                  fontSize: 20,
                  color: StatusColor.Normal,
                ),
                textAlign: TextAlign.left,
              ),
              value: e,
              groupValue: selected.value,
              onChanged: selected,
            );
          }).toList(growable: false),
        );
      }),
    )).dialog(barrierDismissible: false);
    return completer.future;
  }

  Future<Button> _showConfirm() {
    final completer = Completer<Button>();
    DialogGeneral(DialogArgs(
      header: Text(
        '訂單退貨/退款',
        style: const TextStyle(
          fontSize: 20,
          color: const Color(0xff333333),
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
      mainButtonText: '退貨/退款',
      secondaryButtonText: '取消',
      onMainButtonPress: () => completer.complete(Button.Positive),
      onSecondaryButtonPress: () => completer.complete(Button.Negative),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '您確定要退貨/退款嗎？',
            style: const TextStyle(
              fontSize: 16,
              color: kColorError,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    )).dialog(barrierDismissible: false);
    return completer.future;
  }

  Iterable<Widget> _mainChildren() sync* {
    // Header
    yield Obx(() => _header());
    // Body
    yield Expanded(
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: Obx(
          () => ListView(
            padding: EdgeInsets.only(
              bottom: kBottomButtonPadding,
            ),
            children: _listChildren().toList(growable: false),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _listChildren() sync* {
    yield ColoredBox(
      color: const Color(0xFFE2E2E2),
      child: _OrderSummaryView(
        data: controller.data,
        draft: controller.draft,
        tag: tag,
      ),
    );
    yield Divider(
      color: Colors.grey,
      height: 12,
      thickness: 12,
    );
    if (controller.data.subOrderCount == 0) {
      yield _OrderDetailCard(
        tag: tag,
        data: controller.data.data,
        draft: controller.draft,
        apiProvider: controller.apiProvider,
        onEditingDataChanged: () {
          controller.hasEditFormChanged.value = true;
        },
        onWantEditOrders: () async {
          //是否有任何 Orders 變動?
          bool hasOrdersChanged = false;
          //6/24 按下修改餐點當下才做新價格檢查更新。
          //做一次 ordersPostReq 內的商品列表比對，移除過時商品並且跳提示
          if (controller.tryRemoveLegacyProducts()) {
            // 跳提示
            Get.showAlert('已移除部分過時商品!');
            //有移除掉過時商品
            hasOrdersChanged = true;
          }

          //嘗試更新訂單內的商品價格 (只有未完成單才能被編輯，也才能更新價格)
          if (controller.tryUpdateProductPrice()) {
            Get.showAlert('已更新使用最新的商品價格');
            hasOrdersChanged = true;
          }

          //Open the order adjust page for editing.
          final result = await Get.toNamed(
            Routes.ORDERS_ADJUST,
            arguments: OrdersAdjustArgs(
              mode: OrdersAdjustMode.Edit,
              ordersPostReq: controller.draft,
              kind: controller.data.data.kind,
            ),
          );
          if (true == result) {
            hasOrdersChanged = true;
            controller.refreshDraft();
          }

          if (hasOrdersChanged) {
            // print('onEditingDataChanged: do refresh');
            controller.hasEditFormChanged.value = true;
            controller.refreshDraft();
          }
        },
      );
    }
    for (var orderDetail in controller.data.subOrder) {
      yield _OrderDetailCard(
        data: orderDetail,
        tag: tag,
      );
    }
  }

  void _cancelReedit() {
    Get.focusScope.unfocus();
    controller.revertChanges();
  }

  Future<void> _submitReedit() async {
    try {
      Get.focusScope.unfocus();
      if (controller.draft.validateReedit()) {
        final ret = await FutureProgress(
          future: controller.submitReedit(),
        ).dialog<bool>();
        if (true == ret) {
          //
        }
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Widget _bottomBar() {
    //按照當前 order status 以及 hasEditFormChanges 決定下方顯示
    if (controller.hasEditFormChanged.value) {
      //顯示儲存按鈕
      return YesNoButton(
        rightColor: Colors.red,
        leftButtonText: '取消',
        rightButtonText: '儲存',
        onLeftPressed: _cancelReedit,
        onRightPressed: _submitReedit,
      ).paddingSymmetric(horizontal: 20, vertical: 8);
    }
    if (controller.data.data.status == ORDER_STATUS_PROCESSING) {
      return YesNoButton(
        leftButtonText: '棄單',
        rightButtonText: '接單',
        onLeftPressed: () {
          Get.back(result: OrderDetailViewShortCut.RejectOrder);
        },
        onRightPressed: () {
          Get.back(result: OrderDetailViewShortCut.AcceptOrder);
        },
      ).paddingSymmetric(horizontal: 20, vertical: 8);
    }
    if (OrderStatus.Accepted.index == controller.data.data.status) {
      if (PaymentStatus.Paid.index == controller.data.data.paymentStatus) {
        // 已付款顯示退點、歸檔
        return YesNoButton(
          leftButtonText: '列印',
          midButtonText: '退點',
          midColor: const Color(0xff3e4b5a),
          rightButtonText: '歸檔',
          rightColor: const Color(0xff3a6cc9),
          onLeftPressed: _showMore,
          onMidPressed: () {
            Get.back(result: OrderDetailViewShortCut.RejectOrder);
          },
          onRightPressed: () {
            Get.back(result: OrderDetailViewShortCut.CheckoutOrder);
          },
        ).paddingSymmetric(horizontal: 20, vertical: 8);
      }
      return YesNoButton(
        leftButtonText: '列印',
        midButtonText: '棄單',
        rightButtonText: '結帳',
        rightColor: Colors.red,
        onLeftPressed: _showMore,
        onMidPressed: () {
          Get.back(result: OrderDetailViewShortCut.RejectOrder);
        },
        onRightPressed: () {
          Get.back(result: OrderDetailViewShortCut.CheckoutOrder);
        },
      ).paddingSymmetric(horizontal: 20, vertical: 8);
    }
    // 不顯示任何按鈕
    return SizedBox(width: double.infinity);
  }
}

class _OrderDetailCard extends GetView<OrderDetailController> {
  @override
  final String tag;

  final OrderDetail data;

  //這個有給資料才會顯示編輯介面
  final OrdersPostReq draft;
  final ApiProvider apiProvider;
  final Function onEditingDataChanged;
  final Function onWantEditOrders;

  bool get receiverVisibility {
    if (data.receiverAddress == null) {
      return false;
    }
    var ret = false;
    // 餐飲外送需顯示收件人資訊
    if (data.type.orderType.isDinnerDelivery) {
      ret = true;
    }
    // 零售外送需顯示收件人資訊
    if (data.type.orderType.isRetailDelivery) {
      ret = true;
    }
    return ret;
  }

  const _OrderDetailCard({
    Key key,
    @required this.data,
    this.draft,
    this.apiProvider,
    this.onEditingDataChanged,
    this.onWantEditOrders,
    @required this.tag,
  }) : super(key: key);

  Iterable<Widget> _list() sync* {
    yield _member();
    yield SizedBox(
      width: double.infinity,
      child: SvgPicture.asset(
        'assets/images/dot_line_1.svg',
        // width: double.infinity,
        fit: BoxFit.fitWidth,
      ).paddingSymmetric(
        horizontal: 12.0,
      ),
    );
    yield const Divider(
      color: Colors.transparent,
      height: 12,
    );
    yield Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: LabelValue(
            labelText: '訂單編號：',
            // valueText: 'line2021098765',
            valueText: this.data?.orderNumber ?? '',
          ),
        ),
        Text(
          // '03-17 15:20',
          this.data?.displayCreatedAt ?? '',
          style: const TextStyle(
            fontSize: 16,
            color: const Color(0xff666666),
          ),
        ),
      ],
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 4.0,
    );
    yield Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          // '取餐方式：',
          data.displayTakeMethod ?? '',
          style: const TextStyle(
            fontSize: 16,
            color: const Color(0xff666666),
          ),
        ),
        Container(
          padding: kChipPadding,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(const Radius.circular(25.0)),
            // color: kColorPrimary,
            color: data?.displayTypeColor ?? kColorPrimary,
            // border: Border.all(
            //   width: 1.0,
            //   color: Colors.white,
            // ),
          ),
          child: Text(
            // '內用',
            data?.displayType ?? '',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Visibility(
          visible: data.isOrderFromLine,
          child: LineTag(
            text: 'Line下單',
            textColor: kColorLine,
          ),
        ),
      ],
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 4.0,
    );
    yield Builder(builder: (context) {
      bool visible = false;
      if (draft != null && data.editable()) {
        // 可編輯訂單不用顯示桌號資訊，在編輯界面會顯示
        visible = false;
      } else {
        visible = [
          OrderType.DinnerHere,
          OrderType.DinnerToGo,
          OrderType.DinnerOrder,
        ].contains(data.orderType);
      }
      return Visibility(
        visible: visible,
        child: LabelValue(
          labelText: '桌號：',
          // valueText: this.orderDetailData.orderDiner.memo,
          valueText: data.partitionTableName,
        ).paddingSymmetric(
          horizontal: kPadding,
          vertical: 4.0,
        ),
      );
    });
    yield Visibility(
      visible: data.orderType.isRetail,
      child: LabelValue(
        labelText: '付款狀態：',
        valueText: data.displayPaymentStatus,
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    yield LabelValue(
      labelText: '顧客備註：',
      valueText: data.orderDiner.memo,
      // valueText: this.data?.comment ?? '',
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 4.0,
    );
    yield Builder(builder: (context) {
      // 零售不顯示取貨時間
      if (this.data.type.orderType.storeType.isRetail) {
        return SizedBox.shrink();
      }
      // 餐飲顯示用餐時間及大人小孩資訊
      return Row(
        children: [
          Builder(builder: (context) {
            if (this.data.type.orderType.storeType.isDinner) {
              return LabelValue(
                // labelText: '用餐時間：',
                labelText: this.data.displayMealType,
                // valueText: '03-17 17:30',
                valueText: data.displayMealAt,
                valueColor: this.data.displayMealAtColor,
              );
            }
            return SizedBox.shrink();
          }),

          const VerticalDivider(),
          Text(
            // '2大3小',
            this.data?.orderDiner?.displayPeople ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: kColorPrimary,
            ),
            textAlign: TextAlign.right,
          ),
          //Do we actually need this?
          // const VerticalDivider(),
          // Text(
          //   // 'A區3桌'
          //   this.orderDetailData?.orderDiner?.displayTable ?? '',
          //   style: const TextStyle(
          //     fontSize: 16,
          //     color: kColorPrimary,
          //   ),
          //   textAlign: TextAlign.right,
          // ),
        ],
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      );
    });
    yield Visibility(
      visible: data.buyerAddress != null,
      child: Builder(builder: (context) {
        return LabelValue(
          labelText: '訂購人姓名：',
          valueText: data?.displayBuyerName,
        ).paddingSymmetric(
          horizontal: kPadding,
          vertical: 4.0,
        );
      }),
    );
    yield Visibility(
      visible: data.buyerAddress != null,
      child: Builder(builder: (context) {
        return LabelValue(
          labelText: '訂購人電話：',
          valueText: data.displayBuyerPhone,
        ).paddingSymmetric(
          horizontal: kPadding,
          vertical: 4.0,
        );
      }),
    );
    yield Visibility(
      visible: receiverVisibility,
      child: Builder(builder: (context) {
        return LabelValue(
          labelText: '收件人姓名：',
          valueText: data?.displayReceiverName,
        ).paddingSymmetric(
          horizontal: kPadding,
          vertical: 4.0,
        );
      }),
    );
    yield Visibility(
      visible: receiverVisibility,
      child: Builder(builder: (context) {
        return LabelValue(
          labelText: '收件人電話：',
          valueText: data.displayReceiverPhone,
        ).paddingSymmetric(
          horizontal: kPadding,
          vertical: 4.0,
        );
      }),
    );
    yield Visibility(
      visible: receiverVisibility,
      child: LabelValue(
        labelText: '收件人地址：',
        valueText: data.displayReceiverAddress,
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    yield Builder(builder: (context) {
      bool visible = true;
      if (draft != null && data.editable()) {
        visible = false;
      }
      return Visibility(
          visible: visible,
          child: LabelValue(
            labelText: '店家備註：',
            // valueText: this.data.dinerMemo,
            valueText: data?.comment ?? '',
          ).paddingSymmetric(
            horizontal: kPadding,
            vertical: 4.0,
          ));
    });
    yield const Divider(
      color: Colors.transparent,
      height: 12,
    );
    yield Container(
      color: kColorBackground,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: _itemListView().toList(growable: false),
      ),
    );
    yield _coupon();
    final couponDiscount = data.couponDiscount ?? 0;
    if (couponDiscount is num && couponDiscount < 0) {
      yield _memberCouponDiscount(couponDiscount);
    }
    if (couponDiscount is num && couponDiscount > 0) {
      yield _memberCouponExtraPrice(couponDiscount);
    }
    yield _total();
  }

  Iterable<Widget> _itemListView() sync* {
    yield const Divider(
      color: Colors.transparent,
      height: 12,
    );

    //****** Editing Form ******
    yield Visibility(
      visible: draft != null && data.editable(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: kPadding),
        child: OrdersPostReqEditForm(
          tableProvider: controller.tableProvider,
          draft: draft,
          onChanged: onEditingDataChanged,
        ),
      ),
    );

    yield SizedBox(
      width: double.infinity,
      child: SvgPicture.asset(
        'assets/images/dot_line_1.svg',
        fit: BoxFit.fitWidth,
      ).paddingSymmetric(
        horizontal: 12.0,
      ),
    );

    yield _itemListHeader();
    yield const Divider(
      color: Colors.black,
      indent: 12.0,
      endIndent: 12.0,
      height: 1.0,
    );

    // 這邊的商品列表如果有編輯資料就用編輯資料，如果沒有編輯資料就用詳細資料
    yield _orderItemListView(_normalItems);
  }

  Iterable<OrderItem> get _normalItems {
    return draft?.normalItems ?? data?.normalItems ?? <OrderItem>[];
  }

  num get _normalItemsCount {
    return _normalItems.fold<num>(0, (previousValue, element) {
      return previousValue + element.nnQuantity;
    });
  }

  num get _normalItemsPrice {
    return _normalItems.fold<num>(0, (previousValue, element) {
      return previousValue + element.stackPrice;
    });
  }

  Widget _itemListHeader() {
    return Stack(
      children: [
        Align(
          alignment: Alignment.center,
          child: Text(
            '商品明細',
            style: TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
            textHeightBehavior:
                TextHeightBehavior(applyHeightToFirstAscent: false),
            textAlign: TextAlign.center,
          ).paddingSymmetric(
            vertical: 12.0,
          ),
        ),
        // Edit orders button.
        Visibility(
          visible: data.isRetail,
          child: SizedBox.shrink(),
          replacement: Align(
            alignment: Alignment.centerRight,
            child: Visibility(
              visible: draft != null && data.editable(),
              child: TextButton.icon(
                style: ButtonStyle(
                  foregroundColor: MaterialStateProperty.all(kColorPrimary),
                ),
                onPressed: () async {
                  // 6/24 按下修改餐點當下才做新價格檢查更新。
                  onWantEditOrders?.call();
                },
                icon: Icon(Icons.edit),
                label: Text('修改餐點'),
              ),
            ),
          ),
        ),
      ],
    ).paddingSymmetric(horizontal: 12.0);
  }

  Widget _total() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: kPadding, vertical: 12),
      color: kColorBackground,
      alignment: Alignment.centerRight,
      child: Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 17,
            color: OKColor.Gray22,
          ),
          children: _totalChildren().toList(growable: false),
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.left,
      ),
    );
  }

  Iterable<InlineSpan> _totalChildren() sync* {
    yield TextSpan(
      text: '總計：',
      style: TextStyle(
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      // text: '3',
      text: _normalItemsCount.decimalStyle ?? '',
      style: TextStyle(
        color: controller.prefProvider.themeColor,
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      text: ' 項 金額：',
      style: TextStyle(
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      // text: '\$820',
      text: _normalItemsPrice.decimalStyle ?? '',
      style: TextStyle(
        color: controller.prefProvider.themeColor,
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      text: ' 元',
      style: TextStyle(
        fontWeight: FontWeight.w500,
      ),
    );
  }

  Widget _member() {
    final member = controller.memberProvider
        .getMemberFromLocalStorage(data?.memberId ?? 0);
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        vertical: 4.0,
        horizontal: kPadding,
      ),
      // TODO: server 回傳資料缺少 member image
      leading: MemberAvatar(
        imageUrl: member?.avatar ?? '',
      ),
      title: Text(
        // '現場消費者',
        data?.memberName ?? '現場消費者',
        style: const TextStyle(
          fontSize: 20,
          color: Colors.black,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.left,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      // member id 存在時，顯示 line 推播按鈕
      trailing: Visibility(
        visible: data?.memberId != null,
        child: IconButton(
          icon: SvgPicture.asset('assets/images/icon_line.svg'),
          onPressed: _onLinePressed,
        ),
      ),
    );
  }

  Widget _memberCouponDiscount(num value) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      color: kColorBackground,
      alignment: Alignment.centerRight,
      child: Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 17,
            color: OKColor.Gray22,
          ),
          children: [
            TextSpan(
              text: '優惠券折抵：',
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(
              // text: '-\$20',
              text: (value ?? 0).currencyStyle,
              style: TextStyle(
                color: controller.prefProvider.themeColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(
              text: '元',
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget _memberCouponExtraPrice(num value) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      color: kColorBackground,
      alignment: Alignment.centerRight,
      child: Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 17,
            color: OKColor.Gray22,
          ),
          children: [
            TextSpan(
              text: '優惠券金額：',
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(
              // text: '\$0',
              text: (value ?? 0).currencyStyle,
              style: TextStyle(
                color: controller.prefProvider.themeColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(
              text: '元',
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget _coupon() {
    return Obx(() {
      if (controller.coupon != null) {
        return Container(
          padding: EdgeInsets.only(
            bottom: kPadding,
          ),
          alignment: Alignment.center,
          color: kColorBackground,
          child: CouponItem(
            data: controller.coupon,
            // onPressed: _showCouponDetail,
          ),
        );
      }
      return SizedBox.shrink();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        border: const Border(
          bottom: const BorderSide(
            color: const Color(0xff6D7278),
            width: 12.0,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: _list().toList(growable: false),
      ),
    );
  }

  void _onLinePressed() {
    LinePusher().sheet<String>().then(
      (value) {
        if (value.isNotEmpty) {
          final memberId = this.data.memberId;
          final future = this.apiProvider.putMemberMessage(memberId, {
            'message': value,
          });
          FutureProgress<Map>(
            future: future,
            // HACK: test error
            // future: controller.memberProvider.pushMessage('', value),
            // TODO: refactor
            onData: this._showDialogOkay,
            // TODO: refactor
            onError: this._showDialogError,
          ).dialog();
        }
      },
    );
  }

  Widget _showDialogError(Object error) {
    final args = DialogArgs(
      contentIcon: DialogContentIcon.Error,
      // header: Text.rich(
      //   TextSpan(
      //     style: const TextStyle(
      //       fontSize: 20,
      //       color: kColorLine,
      //     ),
      //     children: [
      //       TextSpan(
      //         text: 'LINE ',
      //         style: const TextStyle(
      //           fontWeight: FontWeight.w700,
      //         ),
      //       ),
      //       TextSpan(
      //         text: '留言傳送失敗',
      //         style: const TextStyle(
      //           color: const Color(0xff333333),
      //           fontWeight: FontWeight.w700,
      //         ),
      //       ),
      //     ],
      //   ),
      //   textHeightBehavior:
      //       const TextHeightBehavior(applyHeightToFirstAscent: false),
      //   textAlign: TextAlign.center,
      // ),
      content: Text(
        (error as DioError)?.responseMessage ?? error.toString(),
        style: const TextStyle(
          fontSize: 16,
          color: const Color(0xff666666),
        ),
      ),
      mainButtonText: '確認',
    );
    return DialogGeneral(args);
  }

  Widget _showDialogOkay(Map data) {
    final args = DialogArgs(
      header: Text.rich(
        TextSpan(
          style: const TextStyle(
            fontSize: 16,
            color: kColorLine,
          ),
          children: [
            TextSpan(
              text: 'LINE ',
              style: const TextStyle(
                fontWeight: FontWeight.w700,
              ),
            ),
            TextSpan(
              text: '留言已傳送',
              style: const TextStyle(
                color: const Color(0xff333333),
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
        textHeightBehavior:
            const TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.center,
      ),
      content: Text(
        '會員如果回覆您的Line留言，請至LINE官方帳號APP上查閱。',
        style: const TextStyle(
          fontSize: 16,
          color: const Color(0xff666666),
        ),
        textAlign: TextAlign.center,
      ),
      mainButtonText: '確認',
    );
    return DialogGeneral(args);
  }

  Widget _orderItemListView(Iterable<OrderItem> data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: data.map((item) {
        return UnderlineDivider(
          insets: EdgeInsets.zero,
          backgroundColor: Colors.transparent,
          child: _itemView(item),
        ).paddingSymmetric(
          horizontal: kPadding,
        );
      }).toList(),
    );
  }

  Widget _itemView(OrderItem item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: _itemChildren(item).toList(growable: false),
    );
  }

  Iterable<Widget> _itemChildren(OrderItem item) sync* {
    // 產品名稱
    yield Text(
      // '厚切腰內豬排',
      item.productName ?? '',
      style: const TextStyle(
        fontSize: 17,
        color: const Color(0xff222222),
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      // horizontal: 12,
      vertical: 4.0,
    );
    // 附屬品
    if (item.productSpec1 != null && item.productSpec1.isNotEmpty) {
      yield Text(
        // '半碗飯、不要太多肉',
        item.productSpec1 ?? '',
        style: const TextStyle(
          fontSize: 15,
          color: const Color(0xff6d7278),
        ),
        textAlign: TextAlign.left,
      ).paddingOnly(
        left: kPadding,
        top: 2.0,
        bottom: 2.0,
      );
    }
    // 價格
    yield Row(
      children: [
        Expanded(
          child: Text(
            '${(item.finalPrice ?? 0).currencyStyle} x ${item.quantity ?? 1}',
            style: const TextStyle(
              fontSize: 15,
              color: kColorPrimary,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Text(
          '${item.stackPrice.decimalStyle}元',
          style: const TextStyle(
            fontSize: 15,
            color: kColorPrimary,
          ),
          textAlign: TextAlign.left,
        ),
      ],
    ).paddingOnly(
      left: kPadding,
      top: 2,
      bottom: 4,
    );
  }
}

class _OrderSummaryView extends GetView<OrderDetailController> {
  static const _PADDING = 12.0;
  @override
  final String tag;
  final OrderRoot data;

  //因為價格有可能會被變動因此需要這個編輯中的data
  final OrdersPostReq draft;

  bool get hasInvoice {
    return invoiceNumber?.isNotEmpty ?? false;
  }

  String get invoiceNumber {
    final orderDetail = data.data;
    return orderDetail?.orderInvoice?.number ?? '';
  }

  bool get hasBuyer {
    return this.buyer?.isNotEmpty ?? false;
  }

  String get buyer {
    final orderDetail = data.data;
    final value1 = orderDetail?.orderInvoice?.vatNumber;
    final value2 = orderDetail?.invoiceInfo?.vatNumber;
    return value1 ?? value2 ?? '';
  }

  const _OrderSummaryView({
    Key key,
    @required this.data,
    @required this.draft,
    @required this.tag,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (controller.hasEditFormChanged.value) {
      draft.recalculate();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: _buildChildren().toList(growable: false),
    );
  }

  Iterable<Widget> _buildChildren() sync* {
    yield SizedBox(height: _PADDING);
    yield LabelValue(
      // valueText: '03-17 18:20',
      labelText: '結帳時間：',
      valueText: data.checkoutAt?.MMddHHmm ?? '',
    ).paddingSymmetric(
      vertical: 4.0,
      horizontal: kPadding,
    );
    if (hasInvoice == true) {
      yield Row(
        children: [
          LabelValue(
            // valueText: 'QM-55560938',
            labelText: '發票號碼：',
            valueText: invoiceNumber,
          ),
          const SizedBox(
            width: 8.0,
          ),
          Obx(() {
            return Text(
              // '已作廢',
              controller.displayInvoiceStatus,
              style: const TextStyle(
                color: OKColor.Must,
              ),
            );
          }),
        ],
      ).paddingSymmetric(
        vertical: 4.0,
        horizontal: kPadding,
      );
    }
    if (hasBuyer == true) {
      yield LabelValue(
        labelText: '統一編號：',
        // valueText: '12344321',
        valueText: this.buyer,
      ).paddingSymmetric(
        vertical: 4.0,
        horizontal: kPadding,
      );
    }
    if (controller.hasCarrierId == true) {
      yield LabelValue(
        labelText: '載具：',
        // valueText: '12344321',
        valueText: controller.carrierId,
      ).paddingSymmetric(
        vertical: 4.0,
        horizontal: kPadding,
      );
    }
    if (controller.hasNpoBan == true) {
      yield LabelValue(
        labelText: '愛心碼：',
        // valueText: '12344321',
        valueText: controller.npoBan,
      ).paddingSymmetric(
        vertical: 4.0,
        horizontal: kPadding,
      );
    }
    yield LabelValue(
      labelText: '支付方式：',
      // valueText: '台灣pay',
      valueText: controller.data?.data?.displayOrderPayment,
    ).paddingSymmetric(
      vertical: 4.0,
      horizontal: kPadding,
    );
    yield SizedBox(height: _PADDING);
    yield SizedBox(
      width: double.infinity,
      child: SvgPicture.asset(
        'assets/images/dot_line_1.svg',
        fit: BoxFit.fitWidth,
      ).paddingSymmetric(
        horizontal: 12.0,
      ),
    );
    yield SizedBox(height: _PADDING);
    yield _settlementView().paddingSymmetric(
      horizontal: kPadding,
    );
    yield SizedBox(height: _PADDING);
    yield SizedBox(
      width: double.infinity,
      child: SvgPicture.asset(
        'assets/images/dot_line_2.svg',
        fit: BoxFit.fitWidth,
      ).paddingSymmetric(
        horizontal: 12.0,
      ),
    );
    yield SizedBox(height: _PADDING);
    yield Row(
      children: [
        Expanded(
          child: LabelValue(
            labelText: '商品小計：',
            // valueText: '566',
            valueText: (draft?.normalItemsPrice ?? data?.normalItemsPrice ?? 0)
                .decimalStyle,
          ).paddingSymmetric(
            vertical: 4.0,
          ),
        ),
        Expanded(
          child: Visibility(
            visible: data.storeType.isDinner,
            child: LabelValue(
              labelText: '服務費：',
              // valueText: '56',
              valueText: controller.serviceCharges.decimalStyle,
            ).paddingSymmetric(
              vertical: 4.0,
            ),
            replacement: LabelValue(
              labelText: '運費：',
              // valueText: '56',
              valueText:
                  data.data?.orderShipping?.shippingFee?.decimalStyle ?? '0',
            ).paddingSymmetric(
              vertical: 4.0,
            ),
          ),
        ),
      ],
    ).paddingSymmetric(
      horizontal: kPadding,
    );
    yield SizedBox(height: _PADDING);
    yield Row(
      children: [
        Expanded(
          child: LabelValue(
            labelText: '現場減價：',
            // valueText: '-2',
            valueText:
                data.getDiscountPrice(DiscountType.Discount).decimalStyle,
          ).paddingSymmetric(
            vertical: 4.0,
          ),
        ),
        Expanded(
          child: LabelValue(
            labelText: '額外費用：',
            // valueText: '0',
            valueText: data.additionalCharges.decimalStyle,
          ).paddingSymmetric(
            vertical: 4.0,
          ),
        ),
      ],
    ).paddingSymmetric(
      horizontal: kPadding,
    );
    yield SizedBox(height: _PADDING);
    if (controller.redeemMemberPoints != 0) {
      yield LabelValue(
        // labelText: controller.redeemMemberPointsTitle,
        labelText: '積點折抵：',
        valueText: (-controller.redeemMemberPoints).decimalStyle,
      ).paddingSymmetric(
        horizontal: kPadding,
      );
    }
    if (data.data.storeType.isRetail) {
      yield LabelValue(
        labelText: '金流手續費：',
        // labelText: () {
        //   final name = ordersOrderIdGetRes.data?.orderPayment?.name ?? '';
        //   return '$name：';
        // }(),
        // valueText: '-2',
        valueText: (data.data.orderPaymentFee ?? 0).decimalStyle,
      ).paddingSymmetric(
        vertical: 4.0,
        horizontal: kPadding,
      );
    }
    yield SizedBox(height: _PADDING);
    yield SizedBox(
      width: double.infinity,
      child: SvgPicture.asset(
        'assets/images/dot_line_2.svg',
        fit: BoxFit.fitWidth,
      ).paddingSymmetric(
        horizontal: 12.0,
      ),
    );
    yield SizedBox(height: _PADDING);
    yield _ValueText(
      label: _TextArgs(
        '商品總價：',
        fontWeight: FontWeight.w700,
      ),
      value: _TextArgs(
        draft?.total?.currencyStyle,
        fontWeight: FontWeight.w700,
        color: controller.prefProvider.themeColor,
      ),
    ).paddingSymmetric(
      horizontal: kPadding,
    );
    yield SizedBox(height: _PADDING);
    if (controller.pointGetVisible) {
      yield FutureBuilder<num>(
        future: controller.pointGet,
        builder: (context, snapshot) {
          final points = snapshot.data ?? 0;
          return _ValueText(
            label: _TextArgs(
              '獲得積點：',
              fontWeight: FontWeight.w700,
            ),
            value: _TextArgs(
              snapshot.hasData ? points.decimalStyle : '-',
              fontWeight: FontWeight.w700,
              color: controller.prefProvider.themeColor,
            ),
          ).paddingSymmetric(
            horizontal: kPadding,
          );
        },
      );
    }
    if (draft.modifiable == true) {
      yield Row(
        children: [
          Expanded(
            child: LabelValue(
              labelText: '實收：',
              // valueText: '700',
              valueText: (draft.paid ?? 0).decimalStyle,
            ).paddingSymmetric(
              vertical: 4.0,
            ),
          ),
          Expanded(
            child: LabelValue(
              labelText: '找零：',
              // valueText: '80',
              valueText: (draft.change ?? 0).decimalStyle,
            ).paddingSymmetric(
              vertical: 4.0,
            ),
          ),
        ],
      ).paddingSymmetric(
        horizontal: kPadding,
      );
    }
    yield SizedBox(height: _PADDING);
  }

  ///
  /// 結算統計文字
  ///
  Widget _settlementView() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 16,
          color: Colors.black,
        ),
        children: _textSpan().toList(growable: false),
      ),
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
      ),
      textAlign: TextAlign.left,
    );
  }

  Iterable<InlineSpan> _textSpan() sync* {
    yield TextSpan(
      text: '合計 ',
      style: const TextStyle(
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      text: data.orderCount.decimalStyle,
      style: const TextStyle(
        color: const Color(0xfffa821b),
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      text: ' 訂單，',
      style: const TextStyle(
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      text: (draft.normalItemsCount ?? data.normalItemsCount).decimalStyle,
      style: const TextStyle(
        color: kColorPrimary,
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      text: ' 項，',
      style: const TextStyle(
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      text: '\$${(draft.total ?? data.data.total).decimalStyle}',
      style: const TextStyle(
        color: kColorPrimary,
        fontWeight: FontWeight.w500,
      ),
    );
    yield TextSpan(
      text: ' 元',
      style: const TextStyle(
        color: const Color(0xff222222),
        fontWeight: FontWeight.w500,
      ),
    );
  }
}

class _TextArgs {
  final String text;
  final FontWeight fontWeight;
  final double fontSize;
  final Color color;

  _TextArgs(
    this.text, {
    this.fontWeight,
    this.fontSize,
    this.color,
  });

  TextStyle get style => TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      );
}

class _ValueText extends StatelessWidget {
  final _TextArgs label;
  final _TextArgs value;
  const _ValueText({
    Key key,
    this.label,
    this.value,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        style: const TextStyle(
          fontSize: 20.0,
          color: Colors.black,
          fontWeight: FontWeight.normal,
        ),
        children: [
          TextSpan(
            text: label?.text ?? '',
            style: label?.style,
          ),
          TextSpan(
            text: value?.text ?? '',
            style: value.style,
          ),
        ],
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.left,
    );
  }
}
